import { IPlan, IPlanRequest } from "../models/PlanModel";

export const convertToPlanRequest = (plan: IPlan) => {
  const { part, ...rest } = plan;
  const dataReq: IPlanRequest = {
    id: rest?.id ?? "",
    code: rest?.code ?? "",
    name: rest?.name ?? "",
    year: plan?.year ?? "",
  };
  return dataReq;
};

export const convertToPlanRes = (plan: IPlanRequest) => {
  const dataRes: IPlan = {
    id: plan?.id ?? "",
    code: plan?.code ?? "",
    name: plan?.name ?? "",
    year: plan?.year ?? "",
  };
  return dataRes;
};

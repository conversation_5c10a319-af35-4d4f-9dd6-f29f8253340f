import axios from "axios";
import { urlParamsConfig } from "../../utils/ParamsUtils";
import { localStorageItem } from "../../utils/LocalStorage";
import { KEY_LOCALSTORAGE } from "../../auth/core/_consts";

const API_PATH =
  // localStorageItem.get(KEY_LOCALSTORAGE.CONFIGURATION)?.["apiUrl"] ||
  process.env.REACT_APP_API_URL;

export const getPlans = (params: any) => {
  const url = `${API_PATH}/plan/search`;
  return axios.get(urlParamsConfig(url, params));
};

export const createPlan = (data: any) => {
  const url = `${API_PATH}/plan`;
  return axios.post(url, data);
};

export const updatePlan = (data: any, id: string) => {
  const url = `${API_PATH}/plan/${id}`;
  return axios.put(url, data);
};

export const deletePlan = (id: string) => {
  const url = `${API_PATH}/plan/${id}`;
  return axios.delete(url);
};

export const getPlanById = (id: string) => {
  const url = `${API_PATH}/plan/${id}`;
  return axios.get(url);
};

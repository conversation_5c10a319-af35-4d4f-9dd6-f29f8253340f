import axios from "axios";
import { KEY_LOCALSTORAGE } from "../auth/core/_consts";
import { localStorageItem } from "../utils/LocalStorage";

const API_PATH =
  // localStorageItem.get(KEY_LOCALSTORAGE.CONFIGURATION)?.["apiUrl"] ||
  process.env.REACT_APP_API_URL;

export const fileUpload = (file: any) => {
  let url = `${API_PATH}/file/upload`;
  let formData = new FormData();
  formData.append("uploadFile", file);
  const config = {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  };
  return axios.post(url, formData, config);
};

export const getFileById = (fileId: string) => {
  let url = `${API_PATH}/file/document/${fileId}`;
  return axios.get(url, { responseType: "blob" });
};

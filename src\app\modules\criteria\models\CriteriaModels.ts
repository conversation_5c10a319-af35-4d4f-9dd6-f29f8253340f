import { ObjectSearchMain, OptionSelect } from "../../models/models";

export interface Criteria {
  id: string;
  planId: string;
  planName: string;
  planCode: string;
  year: number;
  partCode: string;
  partName: string;
  chapterCode: string;
  chapterName: string;
  criteriaId: number;
  criteriaCode: string;
  criteriaName: string;
  createdDate: string;
  modifiedDate: string;
  status: number;
  pointInspectorAssessment: number | null;
  pointUnitAssessment: number | null;
  pointSeltAssessment: number | null;
  comment?: string;
}

export interface GroupedData {
  [partCode: string]: {
    partName: string;
    chapters: {
      [chapterCode: string]: {
        chapterName: string;
        criterias: Criteria[];
      };
    };
  };
}

export interface ISearchCriteria extends ObjectSearchMain {
  plan?: OptionSelect | null;
}

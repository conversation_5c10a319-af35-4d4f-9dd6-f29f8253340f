import axios from "axios";
import { urlParamsConfig } from "../../utils/ParamsUtils";
import { localStorageItem } from "../../utils/LocalStorage";
import { KEY_LOCALSTORAGE } from "../../auth/core/_consts";

const API_PATH =
  // localStorageItem.get(KEY_LOCALSTORAGE.CONFIGURATION)?.["apiUrl"] ||
  process.env.REACT_APP_API_URL;

export const getListCriteria = (params: any) => {
  const url = `${API_PATH}/criteria-assessment/plan-criteria`;
  return axios.get(urlParamsConfig(url, params));
};

export const getSubSessionByPlanCriteria = (params: any) => {
  const url = `${API_PATH}/criteria-assessment/search`;
  return axios.get(urlParamsConfig(url, params));
};

export const getLogAssessment = (params: any) => {
  const url = `${API_PATH}/log-assessment/by-assessment`;
  return axios.get(urlParamsConfig(url, params));
};

export const saveCriteria = (data: any) => {
  const url = `${API_PATH}/criteria-assessment`;
  return axios.post(url, data);
};

export const updateStatusAssessment = (data: any) => {
  const url = `${API_PATH}/criteria-assessment/status`;
  return axios.put(url, data);
};

export const getDataReport = (params: any) => {
  const url = `${API_PATH}/criteria-assessment/report`;
  return axios.get(urlParamsConfig(url, params));
};

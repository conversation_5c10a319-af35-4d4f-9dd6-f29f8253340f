import clsx from 'clsx';
import { ChangeEvent, FocusEvent, KeyboardEvent, useEffect, useRef, useState } from 'react';
import { Form } from 'react-bootstrap';
import { KEY } from '../../utils/Constant';
import { IBaseComponentProps } from '../types';

type Cell = {
	type?: 'string' | 'number' | 'all';
	name: string;
	length?: number;
	flex?: number | string;
	cellProps?: any;
};

type Props = IBaseComponentProps & {
	cells: Cell[];
	onChange: (e: ChangeEvent<HTMLInputElement>) => void;
	value: any;
	className?: string;
	inputClassName?: string;
	labelClassName?: string;
	label?: string;
	required?: boolean;
	disabled?: boolean;
	errors?: any;
	touched?: any;
	setFieldTouched?: (name: string, value: boolean, validate?: boolean) => void;
	cellWrapperClassName?: string;
	cellFlex?: number;
};

const InputCellGroup = ({
	cells,
	value,
	onChange,
	className,
	inputClassName,
	labelClassName,
	label,
	required,
	disabled,
	errors,
	touched,
	setFieldTouched,
	readOnly,
	cellWrapperClassName,
	cellFlex,
}: Props) => {
	const [fieldValue, setFieldValue] = useState<any>({});
	const inputsRef = useRef<HTMLInputElement[]>([]);

	useEffect(() => {
		if (typeof value === 'string') {
			const result: any = {};
			let tempValue = value;
			cells.forEach((cell: Cell) => {
				result[cell.name] = tempValue.slice(0, cell.length);
				tempValue = tempValue.slice(cell.length);
			});
			setFieldValue(result);
		} else {
			setFieldValue(value);
		}
	}, [value]);

	const onCellChange = (e: ChangeEvent<HTMLInputElement>, cell: Cell, index: number) => {
		onChange(e);
		const target = e.target as HTMLInputElement;
		const value = target.value;
		const isValidNextCell =
			target.nextSibling && index < cells.length - 1 && value.length === cell.length;
		const isValidBackCell = target.previousSibling && index > 0 && !value;
		const newValue = {
			...fieldValue,
			[target.name]: value,
		};

		setFieldValue(newValue);

		if (isValidNextCell) {
			nextCell(index);
		}

		if (isValidBackCell) {
			backCell(index);
		}
	};

	const nextCell = (index: number) => {
		inputsRef.current[index + 1].focus();
	};

	const backCell = (index: number) => {
		inputsRef.current[index - 1].focus();
	};

	const onCellKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number) => {
		const target = e.target as HTMLInputElement;

		switch (e.key) {
			case KEY.DELETE:
				setFieldValue({
					...fieldValue,
					[target.name]: '',
				});
				break;

			case KEY.ARROW_LEFT:
				if (index >= 0) {
					backCell(index);
				}
				break;

			case KEY.ARROW_RIGHT:
				if (index < cells.length - 1) {
					nextCell(index);
				}
				break;

			case KEY.BACKSPACE:
				if (!target.value && index > 0) {
					backCell(index);
				}
				break;

			default:
				break;
		}
	};

	return (
		<Form.Group className={clsx('d-flex oct-field-wrapper w-100', className)}>
			{label && (
				<Form.Label
					className={clsx(
						'spaces text-lable-input max-content-width mb-0 me-2 min-w-fit-content',
						labelClassName
					)}
					dangerouslySetInnerHTML={{
						__html: required
							? `${label}<strong class='text-danger ps-1'>(*)</strong>`
							: `${label}`,
					}}
				></Form.Label>
			)}
			<div
				className={clsx('d-flex align-items-end spaces w-100 gap-4', cellWrapperClassName)}
			>
				{cells.map((cell: Cell, index: number) => (
					<div
						key={cell.name}
						className="position-relative"
						style={{
							flex: cellFlex ?? cell.flex ?? cell.length,
						}}
					>
						<Form.Control
							ref={(element: HTMLInputElement) => {
								inputsRef.current[index] = element;
							}}
							name={cell.name}
							value={fieldValue?.[cell.name] ?? ''}
							type={cell.type}
							maxLength={cell.length}
							className={clsx(
								'spaces p-6 h-29 min-w-30 text-center radius-4',
								{
									'is-invalid': touched?.[cell.name] && errors?.[cell.name],
								},
								inputClassName
							)}
							onChange={(e: ChangeEvent<HTMLInputElement>) => {
								onCellChange(e, cell, index);
							}}
							onKeyDown={(e: KeyboardEvent<HTMLInputElement>) => {
								onCellKeyDown(e, index);
							}}
							onFocus={(e: FocusEvent<HTMLInputElement>) => {
								e.target.select();
							}}
							onBlur={() => {
								setFieldTouched?.(cell.name, true, true);
							}}
							disabled={disabled}
							readOnly={readOnly}
							{...cell.cellProps}
						/>
						<Form.Control.Feedback
							type="invalid"
							tooltip
							className="field-tooltip-error"
						>
							{errors?.[cell.name]}
						</Form.Control.Feedback>
					</div>
				))}
			</div>
		</Form.Group>
	);
};

export default InputCellGroup;

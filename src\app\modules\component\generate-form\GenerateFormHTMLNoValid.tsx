import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import axios from 'axios';
import clsx from 'clsx';
import moment from 'moment';
import { ChangeEvent, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { Button, Col, Form, FormCheck, Row } from 'react-bootstrap';
import { toast } from 'react-toastify';
import { useDebounce } from '../../../../_metronic/helpers';
import useMultiLanguage from '../../../hook/useMultiLanguage';
import { AppContext } from '../../appContext/AppContext';
import AutocompleteObjectV2 from '../../component/AutocompleteObjectV2';
import LabelRequired from '../../component/LabelRequired';
import TextField from '../../component/TextField';
import { formatDateDTO } from '../../utils/FormatUtils';
import CheckboxGroup from '../checkbox-group';
import DatePickerCustom from '../date-picker/DatePickerCustom';
import InputCellGroup from '../input-cell-group';
import InputOTP from '../input-otp';
import OCTTextValidator from '../text-validator';
import './confirmDialog.scss';
import './generateForm.scss';
import {
	COMPONENT_TYPE,
	generateSearchParams,
	handleThrowResponseMessage,
	isSuccessfulResponse,
	replaceUrl,
} from './GenerateFormConfig';
import { IDialogProps, ItemTypeProps } from './type';

const GenerateFormHTMLNoValid = ({
	isView,
	modelID,
	onChange,
	values,
	errors,
	touched,
	handleChange = () => {},
	listFieldAuto,
	customComponent,
	setFieldValue,
	customSearchObject,
	readOnly,
    fieldConditionalConfig,
}: IDialogProps) => {
	const { lang } = useMultiLanguage();
	const { setIsLoading } = useContext(AppContext);

	const fieldConditionalFnRef = useRef<Map<string, Function>>(new Map());

	const handleChangeFile = async (e: any, item: ItemTypeProps) => {
		let file = e.target.files[0];
		let formData = new FormData();
		formData.append('uploadedFile', file);

		if (item.url) {
			try {
				setIsLoading(true);
				let res = await uploadFile(item?.url, formData);
				const data = res.data;
				if (isSuccessfulResponse(res.status)) {
					setIsLoading(false);
					let attachment = data.file || data?.data;
					let attachments = [];
					let fileDescriptionIds = [];

					attachments.push(attachment);
					fileDescriptionIds.push(attachment?.id);
					await setFieldValue?.(e.target.name, fileDescriptionIds);
					await onChange?.(e.target.name, fileDescriptionIds);
					toast.success(lang('GENERAL.SUCCESS'));
				} else {
					handleThrowResponseMessage(res);
				}
			} catch {
				toast.error(lang('GENERAL.ERROR'));
			} finally {
				setIsLoading(false);
			}
		}
	};

	const uploadFile = (url: string | undefined | null, formData: any) => {
		const config = {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		};
		return axios.post(url || '', formData, config);
	};

	const handleChangeCheck = (name: string, value: any, modelID: string, e: any) => {
		const checkNumberValue = Number(value).toString();
		setFieldValue?.(modelID, {
			...values?.[modelID],
			[name]: checkNumberValue,
		});
		onChange?.(name, checkNumberValue);
	};

	const handleChangeValueText = (
		name: string,
		value: any,
		modelID: string,
		dataComponent?: any
	) => {
		let valueConvert = value;

		if (dataComponent?.inputType === 'number') {
			valueConvert = Number(value);
		}

		if (dataComponent?.type === 'datetime') {
			valueConvert = formatDateDTO(value);
		}

		setFieldValue?.(modelID, {
			...values?.[modelID],
			[name]: valueConvert,
		});
		onChange?.(name, valueConvert);
	};

	const getValueFromTemplate = (keyStr: string, props: any, defaultValue: any = '') => {
		return keyStr.split('.').reduce((acc, currKey) => acc?.[currKey], props) ?? defaultValue;
	};

	const replaceTemplateString = (template: string, data: any) => {
		return template.replace(/\${(.*?)}/g, (_: any, key: any) => {
			const value = getValueFromTemplate(key, data);
			return value !== undefined ? JSON.stringify(value) : '';
		});
	};

	const replaceTemplateValues = (template: any, data: any): any => {
		if (typeof template === 'string') {
			return template.includes('${')
				? JSON.parse(replaceTemplateString(template, data))
				: template;
		}

		if (Array.isArray(template)) {
			return template.map(item => replaceTemplateValues(item, data));
		}

		if (typeof template === 'object' && template !== null) {
			return Object.fromEntries(
				Object.entries(template).map(([key, value]) => [
					key,
					replaceTemplateValues(value, data),
				])
			);
		}

		return template;
	};

	const handleChangeValueSelect = (value: any, modelID: string, dataComponent?: any) => {
		let type = 'select';
		const customField = replaceTemplateValues(dataComponent.properties, value);
		const key = dataComponent?.key || '';

		setFieldValue?.(modelID, {
			...values?.[modelID],
			[key]: value?.code || value?.id,
			[`${key}Id`]: value?.id,
			[`${key}Name`]: value?.name,
			[`${key}Code`]: value?.code,
			...customField,
		});

		onChange?.(dataComponent?.key, value, type);
	};

	const debouncedValues = useDebounce(values, 500);
	const [searchData, setSearchData] = useState(generateSearchParams(listFieldAuto));

	useEffect(() => {
		const newSearchData = { ...searchData };

		Object.keys(newSearchData).forEach(key => {
			Object.keys(newSearchData[key]).forEach(keySearch => {
				const newValue = values[modelID]?.[keySearch];
				if (newValue !== newSearchData[key][keySearch] && newValue !== undefined) {
					newSearchData[key][keySearch] = newValue;
				}
			});
		});

		setSearchData(newSearchData);
	}, [debouncedValues]);

	const handleGetDataByUrlData = (urlData?: string, searchObject?: any, dataComponent?: any) => {
		return axios.get(urlData as string);
	};

	const generateHTMLByTag = (dataComponent: ItemTypeProps) => {
		const fieldValue = dataComponent?.customConditional
			? calculateValue(dataComponent, isView || dataComponent.disabled)
			: dataComponent?.content ?? '';

		const componentMapper: Record<string, any> = {
			span: (
				<div>
					<LabelRequired
						className="min-w-100px"
						label={values?.[dataComponent?.key] || ''}
					/>
				</div>
			),
			p: (
				<div className={`spaces mt-1 ${dataComponent?.className}`}>
					<LabelRequired
						className={clsx('min-w-100px', dataComponent?.properties?.className)}
						isRequired={dataComponent?.className === 'isRequired'}
						label={fieldValue}
					/>
				</div>
			),
			label: (
				<label
					dangerouslySetInnerHTML={{
						__html: fieldValue,
					}}
					className={clsx(
						'text-lable-input max-content-width',
						dataComponent?.properties?.className,
					)}
				></label>
			),
		};

		const defaultComponent = <div className="hyperlink">{dataComponent?.label}</div>;

		return componentMapper[dataComponent?.tag ?? ''] || defaultComponent;
	};

	const calculateValue = (dataComponent: ItemTypeProps, isDisabled: boolean = false) => {
		try {
			const formValues = values[modelID];
			const isExistField = fieldConditionalFnRef.current.has(dataComponent.key);

			if (!formValues) return null;

			if (!isExistField) return formValues[dataComponent.key] ?? '';

			const conditionalResult = fieldConditionalFnRef.current.get(dataComponent.key)!(
				formValues,
				formValues[dataComponent.key],
				isDisabled,
				dataComponent,
                moment,
                ...Object.values(fieldConditionalConfig ?? {}),
			);

			return conditionalResult?.value ?? null;
		} catch (error) {
			console.error(error);
		}
	};

	const renderHTML = (dataForm: any, configKey?: string) => {
		if (dataForm?.components || dataForm?.[configKey || 'components']) {
			let newArray = dataForm?.components || dataForm?.[configKey || ''] || [];

			if (Object.prototype.toString.call(newArray) === '[object Object]') {
				newArray = Object.entries(newArray).map(([key, value]) => {
					return value;
				});
			}

			let filteredComponents = newArray?.filter(
				(item: ItemTypeProps) => item.type !== COMPONENT_TYPE.BUTTON
			);

			return filteredComponents?.map((dataComponent: ItemTypeProps) => {

				if (dataComponent.customConditional) {
					const params = [
					  'formValues',
					  'fieldValue',
					  'isDisabled',
					  'dataComponent',
                      'moment',
                      ...Object.keys(fieldConditionalConfig ?? {}),
					];
					fieldConditionalFnRef.current.set(dataComponent.key, 
					  // eslint-disable-next-line no-new-func
					  new Function(...params, dataComponent.customConditional)
					);
				}

				const fieldValue = calculateValue(dataComponent, isView || dataComponent.disabled);

				switch (dataComponent?.type) {
					case COMPONENT_TYPE.EMAIL:
					case COMPONENT_TYPE.PASSWORD:
					case COMPONENT_TYPE.TEXTFIELD:
						const isDisabled = isView || dataComponent.disabled;

						return (
							<>
								<div className={`spaces pt-8 ${dataComponent?.overlay?.style}`}>
									<TextField
										hideLabel={dataComponent?.hideLabel}
										label={dataComponent?.label}
										name={dataComponent?.key}
										type={dataComponent.inputType}
										onChange={(e: any) =>
											handleChangeValueText(
												e.target.name,
												e.target.value,
												modelID
											)
										}
										value={fieldValue}
										touched={touched?.[modelID]?.[dataComponent?.key]}
										errors={errors?.[modelID]?.[dataComponent?.key]}
										placeholder={dataComponent.placeholder}
										disabled={isDisabled}
										className={dataComponent?.customClass}
										readOnly={readOnly}
										{...dataComponent.properties}
									/>
								</div>
							</>
						);
					case COMPONENT_TYPE.COLUMNS:
						return (
							<Row>
								{dataComponent?.columns &&
									dataComponent?.columns?.length > 0 &&
									dataComponent?.columns?.map(
										(dataColumn: ItemTypeProps, index: number) => {
											return (
												<Col
													key={index}
													lg={{
														span: dataColumn?.width,
														offset: dataColumn?.offset || 0,
													}}
													className={clsx(dataComponent?.properties?.columnClassName, dataComponent?.customClass)}
												>
													{renderHTML(dataColumn)}
												</Col>
											);
										}
									)}
							</Row>
						);
					case COMPONENT_TYPE.FILE:
						return (
							<>
								<Form.Group controlId="formFile" className="mb-3">
									<Form.Label className="text-lable-input lable m-0">
										{dataComponent?.label}
									</Form.Label>
									<Form.Control
										type="file"
										name={dataComponent.key}
										max={dataComponent?.fileMaxSize}
										min={dataComponent?.fileMinSize}
										accept={dataComponent?.filePattern}
										onChange={e => handleChangeFile(e, dataComponent)}
										value={values?.[dataComponent?.key]}
										disabled={isView}
									/>
								</Form.Group>
							</>
						);
					case COMPONENT_TYPE.DAY:
						const isNgaySinh = dataComponent?.key === 'ngaySinh';
						return (
							<>
								{!dataComponent?.hideLabel && (
									<Form.Label>{dataComponent?.label}</Form.Label>
								)}
								<Row>
									<Col lg={isNgaySinh ? 3 : 4} className="spaces pr-0 mb-3 pt-8">
										<OCTTextValidator
											hideLabel={dataComponent?.hideLabel}
											lable={''}
											name={dataComponent.placeholder}
											type={'text'}
											onChange={handleChange}
											isRequired={dataComponent?.validate?.required}
											value={values?.[dataComponent?.key]}
											touched={touched?.[dataComponent?.key]}
											errors={errors?.[dataComponent?.key]}
											placeholder={''}
											disabled={isView}
										/>
									</Col>
									<Col
										lg={isNgaySinh ? 3 : 4}
										className="spaces mb-3 pl-0 pr-0 pt-8"
									>
										<OCTTextValidator
											hideLabel={dataComponent?.hideLabel}
											lable={''}
											name={dataComponent.placeholder}
											type={'text'}
											onChange={handleChange}
											isRequired={dataComponent?.validate?.required}
											value={values?.[dataComponent?.key]}
											touched={touched?.[dataComponent?.key]}
											errors={errors?.[dataComponent?.key]}
											placeholder={''}
											disabled={isView}
										/>
									</Col>
									<Col lg={isNgaySinh ? 3 : 4} className="spaces mb-3 pl-0 pt-8">
										<OCTTextValidator
											hideLabel={dataComponent?.hideLabel}
											lable={''}
											name={dataComponent.placeholder}
											type={'text'}
											onChange={handleChange}
											isRequired={dataComponent?.validate?.required}
											value={values?.[dataComponent?.key]}
											touched={touched?.[dataComponent?.key]}
											errors={errors?.[dataComponent?.key]}
											placeholder={''}
											disabled={isView}
										/>
									</Col>
									{isNgaySinh && (
										<>
											<Col lg={1} className="spaces mb-3 pl-0 pt-8">
												<div className="spaces d-flex flex-center fw-bold">
													-
												</div>
											</Col>

											<Col lg={2} className="spaces mb-3 pl-0 pt-8">
												<OCTTextValidator
													hideLabel={dataComponent?.hideLabel}
													lable={''}
													name={dataComponent?.key}
													type={'text'}
													onChange={handleChange}
													isRequired={dataComponent?.validate?.required}
													value={values?.[dataComponent?.key]}
													touched={touched?.[dataComponent?.key]}
													errors={errors?.[dataComponent?.key]}
													placeholder={''}
													disabled={isView}
												/>
											</Col>
										</>
									)}
								</Row>
							</>
						);
					case COMPONENT_TYPE.TIME:
						return (
							<>
								{!dataComponent?.hideLabel && (
									<Form.Label>{dataComponent?.label}</Form.Label>
								)}
								<Form.Control
									type="time"
									placeholder="Time"
									onChange={handleChange}
									value={values?.[dataComponent?.key]}
								/>
							</>
						);
					case COMPONENT_TYPE.DATETIME:
						return (
							<div className={`spaces pt-8 ${dataComponent?.overlay?.style}`}>
								<DatePickerCustom
									name={dataComponent?.key}
									value={fieldValue}
									setDateValue={date => {
										handleChangeValueText(
											dataComponent?.key,
											date,
											modelID,
											dataComponent
										);
									}}
									touched={touched?.[modelID]?.[dataComponent?.key]}
									errors={errors?.[modelID]?.[dataComponent?.key]}
									placeholder={dataComponent.placeholder}
									disabled={isView}
									label={dataComponent.hideLabel ? '' : dataComponent?.label}
									readOnly={readOnly}
									{...dataComponent.properties}
								/>
							</div>
						);
					case COMPONENT_TYPE.NUMBER:
					case COMPONENT_TYPE.PHONE:
						return (
							<div className={`spaces pt-8 ${dataComponent?.overlay?.style}`}>
								<TextField
									hideLabel={dataComponent?.hideLabel}
									lable={dataComponent?.label}
									name={dataComponent?.key}
									type={'number'}
									onChange={(e: any) =>
										handleChangeValueText(
											e.target.name,
											e.target.value,
											modelID,
											dataComponent
										)
									}
									value={fieldValue}
									touched={touched?.[modelID]?.[dataComponent?.key]}
									errors={errors?.[modelID]?.[dataComponent?.key]}
									placeholder={dataComponent.placeholder}
									disabled={isView}
								/>
							</div>
						);
					case COMPONENT_TYPE.TEXTAREA:
						return (
							<TextField
								name={dataComponent?.key}
								className={clsx(
									'text-field-label-down-line resize-none',
									dataComponent?.customClass
								)}
								labelClassName="ps-2"
								onChange={(e: any) =>
									handleChangeValueText(e.target.name, e.target.value, modelID)
								}
								as="textarea"
								rows={dataComponent?.rows}
								disabled={isView}
								value={fieldValue}
								touched={touched?.[modelID]?.[dataComponent?.key]}
								errors={errors?.[modelID]?.[dataComponent?.key]}
								label={dataComponent?.hideLabel ? '' : dataComponent?.label}
								containerClassName='spaces pt-10'
								readOnly={readOnly}
								{...dataComponent.properties}
							/>
						);
					case COMPONENT_TYPE.CHECKBOX:
						return (
							<Form.Group
								className={clsx(
									'spaces pt-10 form-check-label-black',
									dataComponent?.customClass
								)}
								controlId={dataComponent?.key}
							>
								<FormCheck
									name={dataComponent.key}
									type="checkbox"
									label={dataComponent?.hideLabel ? '' : dataComponent?.label}
									className={
										clsx(
											"d-flex align-items-center spaces gap-7", 
											dataComponent.properties?.className
										)
									}
									checked={String(fieldValue) === '1'}
									onChange={(e: any) =>
										handleChangeCheck(
											e.target.name,
											e.target.checked,
											modelID,
											e
										)
									}
									disabled={isView || readOnly}
									{...dataComponent.properties}
								/>
							</Form.Group>
						);
					case COMPONENT_TYPE.SELECTBOXES:
						return (
							<CheckboxGroup
								data={dataComponent?.values}
								name={dataComponent?.key}
								label={dataComponent?.hideLabel ? '' : dataComponent?.label}
								disabled={isView || readOnly}
								inline={dataComponent?.inline}
								containerClassName={clsx(
									'spaces mt-15',
									dataComponent.properties?.containerClassName
								)}
								mode={dataComponent.properties?.mode ?? 'single'}
								value={fieldValue}
								onChange={(value: any) => {
									handleChangeValueText(
										dataComponent?.key,
										value,
										modelID,
										dataComponent
									);
								}}
							/>
						);
					case COMPONENT_TYPE.RADIO:
						return (
							<>
								{!dataComponent?.hideLabel && (
									<Form.Label>{dataComponent?.label}</Form.Label>
								)}
								<Form.Group
									className="d-flex spaces pt-8"
									controlId="formBasicCheckbox"
								>
									{dataComponent?.values?.map((dataItem: any) => (
										<Form.Check
											inline
											type="radio"
											label={dataItem?.label}
											value={dataItem.value}
											name={dataComponent?.key}
											checked={fieldValue ===dataItem.value}
											onChange={() => {
												onChange(dataComponent?.key, dataItem.value);
											}}
											disabled={isView}
										/>
									))}
								</Form.Group>
							</>
						);
					case COMPONENT_TYPE.SELECT:
						{
							const options = dataComponent.data?.values;
							let newOptions = [];

							const hasEmptyValue = options.some(
								(item: { value: string; label: string }) =>
									item.label === '' || item.value === ''
							);

							if (!hasEmptyValue) {
								newOptions = options.map((item: { value: string; label: string }) => ({
									name: item.label,
									code: item.value,
								}));
							}
							return (
								<div className={dataComponent?.overlay?.style}>
									<AutocompleteObjectV2
										options={newOptions}
										isSearchDefauilt={true}
										name={dataComponent?.key}
										onChange={(e: any) =>
											handleChangeValueSelect(e, modelID, dataComponent)
										}
										searchFunction={
											dataComponent?.data?.url
											?
											() =>
											handleGetDataByUrlData(
												replaceUrl(dataComponent?.data?.url || ''),
												{
													...customSearchObject?.[dataComponent?.key],
													...searchData?.[dataComponent?.key],
												},
												dataComponent
											)
											: null
										}
										value={fieldValue}
										touched={touched?.[modelID]?.[dataComponent?.key]}
										errors={errors?.[modelID]?.[dataComponent?.key]}
										placeholder={dataComponent?.placeholder || ''}
										searchObject={{
											...customSearchObject?.[dataComponent?.key],
											...searchData?.[dataComponent?.key],
										}}
										className="autocomplete-custom-renderform spaces radius-3 width-100 h-29"
										isDisabled={isView}
										label={dataComponent?.hideLabel ? '' : dataComponent?.label}
										isReadOnly={readOnly}
										containerClassName='spaces mt-8'
										getOptionLabel={(option) => dataComponent?.properties?.displayField 
											? option[dataComponent?.properties?.displayField]
											: option.name
										}
										getOptionValue={(option) => dataComponent?.properties?.valueField 
											? option[dataComponent?.properties?.valueField]
											: option.code
										}
										{...dataComponent.properties}
									/>
								</div>
							);
						}
					case COMPONENT_TYPE.BUTTON:
						return (
							<>
								<Button>
									{lang(`BTN.${dataComponent?.label?.toUpperCase()}`)}
								</Button>
							</>
						);
					case COMPONENT_TYPE.CONTENT:
						return (
							<div className="spaces pt-10 ">
								<CKEditor
									editor={ClassicEditor}
									disabled={isView}
									onChange={(event: any, editor: any) => {
										onChange(dataComponent?.key, editor.getData());
									}}
									data={values?.[dataComponent?.key] || ''}
									// data={values?.[dataComponent?.key] || ""}
								/>
							</div>
						);
					case COMPONENT_TYPE.HTML:
						return (
							<div
								className={clsx(
									'spaces pt-10',
									dataComponent?.properties?.containerClassName
								)}
							>
								{generateHTMLByTag(dataComponent)}
							</div>
						);
					case COMPONENT_TYPE.OTP:
						return (
							<div
								className={clsx(
									`spaces pt-8`,
									dataComponent?.overlay?.style,
									dataComponent?.className
								)}
							>
								<InputOTP
									length={dataComponent?.length || 2}
									value={fieldValue}
									label={dataComponent.hideLabel ? '' : dataComponent?.label}
									onChange={(value: string) => {
										handleChangeValueText(dataComponent?.key, value, modelID);
									}}
									disabled={isView}
									readOnly={readOnly}
								/>
							</div>
						);
					case COMPONENT_TYPE.INPUT_CELL_GROUP:
						return (
							<div 
								className={clsx(
									`spaces pt-8`,
									dataComponent?.overlay?.style,
									dataComponent?.className
								)}
							>
								<InputCellGroup
									cells={dataComponent?.cells || []}
									value={values[modelID]}
									label={dataComponent.hideLabel ? '' : dataComponent?.label}
									onChange={(e: ChangeEvent<HTMLInputElement>) => {
										handleChangeValueText(e.target.name, e.target.value, modelID);
									}}
									disabled={isView}
									readOnly={readOnly}
									cellFlex={dataComponent.cellFlex}
									inputClassName={dataComponent.inputClassName}
									cellWrapperClassName={dataComponent.cellWrapperClassName}
								/>
							</div>
						);
					case COMPONENT_TYPE.CUSTOM:
						let searchDataCustom: any = null;
						if (dataComponent.searchData) {
							searchDataCustom = replaceTemplateValues(
								dataComponent.searchData,
								values[modelID]
							);
						}
						return customComponent ? (
							customComponent({
								data: {
									componentName: dataComponent.key,
									values: values?.[modelID] || '',
									isView,
									searchData: searchDataCustom,
									readOnly,
									properties: dataComponent,
									label: dataComponent.hideLabel ? undefined : dataComponent.label,
									modelID,
								},
							})
						) : (
							<></>
						);
					default:
						return <></>;
				}
			});
		} else {
			return <></>;
		}
	};
	
	return <Form>{renderHTML(listFieldAuto)}</Form>;
};

export default GenerateFormHTMLNoValid;

import React from 'react';
import { styles } from '../../component/phieu-in/constant'
import Signature from '../../component/phieu-in/Signature'
import { Criteria, GroupedData } from '../models/CriteriaModels';

type Props = {
  data: any;
}

const TemplateSummarizeReport = (props: Props) => {
  const { data } = props;

  const groupData = (data: Criteria[]): GroupedData => {
    return data?.reduce((acc, item) => {
      const { partCode, partName, chapterCode, chapterName } = item;

      if (!acc[partCode]) {
        acc[partCode] = {
          partName,
          chapters: {},
        };
      }

      if (!acc[partCode].chapters[chapterCode]) {
        acc[partCode].chapters[chapterCode] = {
          chapterName,
          criterias: [],
        };
      }

      acc[partCode].chapters[chapterCode].criterias.push(item);

      return acc;
    }, {} as GroupedData);
  }

  return (
    <div>
      <div style={{ ...styles.d_flex_j_around, ...styles.marginBottom._20px }}>
        <div style={{ ...styles.textAlign.center }}>
          <div style={{ ...styles.fontBold, ...styles.textUppercase, ...styles.fontSize_16 }}>{data?.unit ?? ""}</div>
          <div style={{ ...styles.fontBold, ...styles.textUppercase, ...styles.fontSize_16 }}>{data?.hospital ?? ""}</div>
        </div>

        <div>
          <div style={{ ...styles.textAlign.center }}>
            <div style={{ ...styles.fontBold, ...styles.textUppercase, ...styles.fontSize_16 }}>Công hòa xã hội chủ nghĩa Việt Nam</div>
            <div style={{ ...styles.fontBold, ...styles.fontSize_16 }}>Độc lập - Tự do - Hạnh phúc</div>
          </div>
          <div style={{ ...styles.fontSize_16, ...styles.fontBold, ...styles.font_italic, ...styles.textAlign.right }}>Quốc Oai, ngày ..... tháng ..... năm ..........</div>
        </div>
      </div>


      <div style={{ ...styles.fontBold, ...styles.textUppercase, ...styles.fontSize_16, ...styles.textAlign.center, ...styles.marginBottom._20px }}>Kết quả tự đánh giá tiêu chí chất lượng</div>

      <div className="overflow-auto" style={styles.marginBottom._20px}>
        <table style={styles.table}>
          <thead>
            <tr>
              <th style={{ ...styles.border1, ...styles.textAlign.center, ...styles.textUppercase }}>Tiêu chí</th>
              <th style={{ ...styles.border1, ...styles.textAlign.center, ...styles.textUppercase }}>Nội dung đánh giá</th>
              <th style={{ ...styles.border1, ...styles.textAlign.center, ...styles.textUppercase }}>{data?.planCriterias?.[0]?.year || "2025"}</th>
              <th style={{ ...styles.border1, ...styles.textAlign.center, ...styles.textUppercase }}>BV tự chấm</th>
              <th style={{ ...styles.border1, ...styles.textAlign.center, ...styles.textUppercase }}>Đoàn KT</th>
              <th style={{ ...styles.border1, ...styles.textAlign.center, ...styles.textUppercase }}>Ghi chú</th>
            </tr>
          </thead>

          <tbody>
            {Object.entries(groupData(data?.planCriterias) || {}).map(([partCode, part]) => (
              <React.Fragment key={partCode}>
                {/* Phần */}
                <tr>
                  <td className='bg-part' colSpan={6} style={{ backgroundColor: "gray", ...styles.fontBold, ...styles.border1 }}>Phần {partCode}: {part.partName}</td>
                </tr>

                {Object.entries(part.chapters).map(([chapterCode, chapter]) => (
                  <React.Fragment key={chapterCode}>
                    {/* Chương */}
                    <tr>
                      <td className='bg-chapter' colSpan={6} style={{ backgroundColor: "#ccc", ...styles.fontBold, ...styles.border1 }}>{chapterCode}: {chapter.chapterName}</td>
                    </tr>

                    {/* Các tiêu chí */}
                    {chapter.criterias.map((item) => (
                      <tr key={item.criteriaId}>
                        <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.width._60px }}>{item.criteriaCode}</td>
                        <td style={{ ...styles.border1 }}>{item.criteriaName}</td>
                        <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.width._60px }}></td>
                        <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.width._60px }}>{item.pointSeltAssessment ?? ""}</td>
                        <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.width._60px }}>{item.pointInspectorAssessment ?? ""}</td>
                        <td style={{ ...styles.border1, ...styles.minWidth._150px }}>{item?.comment ?? ""}</td>
                      </tr>
                    ))}
                  </React.Fragment>
                ))}
              </React.Fragment>
            ))}

            <tr>
              <td style={{ ...styles.border1 }}></td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>Tổng điểm</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.totalPoint || ""}</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.totalPoint || ""}</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.totalPoint || ""}</td>
              <td style={{ ...styles.border1 }}></td>
            </tr>
            <tr>
              <td style={{ ...styles.border1 }}></td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>Số tiêu chí áp dụng (C3, C5 nhân hệ số)</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.totalCriteria || ""}</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.totalCriteria || ""}</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.totalCriteria || ""}</td>
              <td style={{ ...styles.border1 }}></td>
            </tr>
            <tr>
              <td style={{ ...styles.border1 }}></td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>Số tiêu chí không áp dụng</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.totalCriteriaNotUse || ""}</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.totalCriteriaNotUse || ""}</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.totalCriteriaNotUse || ""}</td>
              <td style={{ ...styles.border1 }}></td>
            </tr>
            <tr>
              <td style={{ ...styles.border1 }}></td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>Điểm trung bình chung của các tiêu chí</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.averagePoint || ""}</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.averagePoint || ""}</td>
              <td style={{ ...styles.border1, ...styles.textAlign.center, ...styles.fontBold }}>{data?.averagePoint || ""}</td>
              <td style={{ ...styles.border1 }}></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div style={{ ...styles.d_flex_j_end, ...styles.marginRight._100px }}>
        <Signature title='Giám đốc' />
      </div>
    </div>
  )
}

export default TemplateSummarizeReport
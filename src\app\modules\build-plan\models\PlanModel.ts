import { ObjectSearchMain, OptionSelect } from "../../models/models";

export interface IPlan {
  id?: string;
  code: string;
  name: string;
  part?: OptionSelect | null;
  partId?: string;
  year?: string;
}

export interface IPlanRequest {
  id?: string;
  code: string;
  name: string;
  partId?: string;
  year?: string;
}

export interface IPlanSearch extends ObjectSearchMain {
  startDate?: string;
  endDate?: string;
}

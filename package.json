{"name": "oceantech-ui", "version": "8.1.5", "private": true, "homepage": "/", "dependencies": {"@ckeditor/ckeditor5-build-classic": "^40.2.0", "@ckeditor/ckeditor5-react": "^6.2.0", "@formatjs/intl-pluralrules": "4.3.3", "@formatjs/intl-relativetimeformat": "10.0.1", "@fortawesome/fontawesome-free": "6.1.1", "@oceantech/oceantech-ui": "^0.2.8", "@popperjs/core": "2.11.6", "@reduxjs/toolkit": "^1.9.3", "@tinymce/tinymce-react": "^5.1.1", "animate.css": "4.1.1", "apexcharts": "3.35.0", "axios": "0.26.1", "bootstrap": "5.2.2", "bootstrap-icons": "^1.5.0", "chart.js": "3.7.1", "clsx": "1.1.1", "fast-xml-parser": "^4.5.2", "file-saver": "^2.0.5", "formik": "2.2.9", "fuse.js": "^7.0.0", "html-docx-js-typescript": "^0.1.5", "jwt-decode": "^3.1.2", "line-awesome": "1.3.0", "moment": "^2.29.4", "nouislider": "15.5.1", "plop": "^3.1.2", "prism-react-renderer": "1.3.1", "prism-themes": "1.9.0", "prismjs": "1.28.0", "qs": "6.10.3", "react": "18.0.0", "react-apexcharts": "1.4.0", "react-bootstrap": "2.5.0-beta.1", "react-copy-to-clipboard": "5.1.0", "react-cropper": "^2.3.3", "react-datepicker": "^7.3.0", "react-doc-viewer": "^0.1.14", "react-dom": "18.0.0", "react-file-viewer": "^1.2.1", "react-inlinesvg": "3.0.0", "react-intl": "5.25.0", "react-mustache-template-component": "^2.1.2", "react-query": "3.38.0", "react-redux": "^8.0.5", "react-router-dom": "6.3.0", "react-scripts": "5.0.1", "react-select": "^5.7.2", "react-table": "^7.7.0", "react-toastify": "^9.1.2", "react-topbar-progress-indicator": "4.1.1", "react-virtualized": "^9.22.5", "react-webcam": "^7.2.0", "react-xml-viewer": "^2.0.4", "redux": "^4.2.1", "sass": "1.50.1", "socicon": "3.0.5", "yup": "0.32.11"}, "devDependencies": {"@testing-library/jest-dom": "5.16.4", "@testing-library/react": "13.1.1", "@testing-library/user-event": "13.5.0", "@types/bootstrap": "5.1.10", "@types/chart.js": "2.9.37", "@types/file-saver": "^2.0.7", "@types/jest": "27.4.1", "@types/node": "16.11.27", "@types/prismjs": "1.26.0", "@types/qs": "6.9.7", "@types/react": "18.0.6", "@types/react-copy-to-clipboard": "5.0.2", "@types/react-dom": "18.0.2", "@types/react-table": "^7.7.9", "@types/react-virtualized": "^9.21.30", "@types/sass-loader": "8.0.3", "css-loader": "6.7.1", "del": "6.0.0", "mini-css-extract-plugin": "2.6.1", "prettier": "2.6.2", "rtlcss-webpack-plugin": "4.0.7", "sass-loader": "13.0.2", "typescript": "4.6.3", "webpack": "5.74.0", "webpack-cli": "4.10.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "prettier --check .", "format": "prettier --write .", "rtl": "webpack --config=rtl.config.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintIgnore": ["dist/*"]}
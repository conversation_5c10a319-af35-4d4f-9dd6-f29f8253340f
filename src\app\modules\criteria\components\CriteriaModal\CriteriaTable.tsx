import { OctTable } from "@oceantech/oceantech-ui";
import React, { useContext, useEffect, useState } from "react";
import { Button, Form } from "react-bootstrap";
import AutocompleteV2 from "../../../component/AutocompleteObjectV2";
import TextValidator from "../../../component/TextValidator";
import {
  actualFileType,
  CODE_SUCCESS,
  DEFAULT_FILE_TYPES,
  ERROR_MESSAGE,
  MESSAGE,
  textSystem,
} from "../../../utils/Constant";
import {
  EVALUATE_STATUS,
  RESULT,
  TYPE_MODAL,
} from "../../constants/CriteriaModalConstant";
import {
  saveCriteria,
  updateStatusAssessment,
} from "../../services/CriteriaServices";
import { toast } from "react-toastify";

import { IFile, IViewFile } from "../../../models/models";
import { AppContext } from "../../../appContext/AppContext";
import FormInPhieuDialog from "../../../component/button-in-phieu/components/PrintDialog";
import ViewFile from "../../modals/ViewFile";
import {
  handleUploadFileUtil,
  handleViewFileUtil,
  handleDownloadFileUtil,
} from "../../../utils/FileUtils";
import { FileActions } from "./FileActions";

type Props = {
  dataTable: any;
  setDataTable: any;
  objectSearch: any;
  setObjectSearch: any;
  configTable: any;
  setConfigTable: any;
  handleDoubleClick: any;
  type: string;
  tableCache: any;
  setTableCache: any;
  reload: boolean;
  setReload: any;
};

const CriteriaTable = (props: Props) => {
  const {
    dataTable,
    setDataTable,
    objectSearch,
    setObjectSearch,
    configTable,
    setConfigTable,
    handleDoubleClick,
    type,
    tableCache,
    setTableCache,
    reload,
    setReload,
  } = props;
  const { setIsLoading } = useContext(AppContext);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});
  const [viewFile, setViewFile] = useState<IViewFile>({
    open: false,
    file: null,
  });
  console.log(viewFile);
  

  const validateFields = () => {
    const newErrors: { [key: string]: string } = {};
    let valid = true;

    dataTable?.forEach((row: any, index: number) => {
      if (!row.pointSelfAssessment) {
        newErrors[`pointSelfAssessment_${index}`] = MESSAGE.REQUIRED;
        valid = false;
      }

      if (!row.evidence) {
        newErrors[`evidence_${index}`] = MESSAGE.REQUIRED;
        valid = false;
      }

      if (!row.conclusionAssessment && type === TYPE_MODAL.EVALUATE) {
        newErrors[`conclusionAssessment_${index}`] = MESSAGE.REQUIRED;
        valid = false;
      }
    });

    setErrors(newErrors);
    return valid;
  };

  // useEffect(() => {
  //   validateFields();
  // }, [dataTable]);

  const handleViewFile = async (file: IFile) => {
    const viewFile = await handleViewFileUtil(file, setIsLoading);
    if (viewFile) {
      setViewFile(viewFile);
    }
  };

  const handleDownloadFile = async (file: IFile) => {
    await handleDownloadFileUtil(file, setIsLoading);
  };

  const handleChangeTable = async (
    index: number,
    value: any,
    field: string,
    row: any
  ) => {
    const newDataTable = [...dataTable];

    if (field === "evidence") {
      newDataTable[index][field] = await handleUploadFileUtil(
        value,
        setIsLoading
      );
    } else if (field === "status") {
      newDataTable[index][field] = value
        ? EVALUATE_STATUS.EVALUATED.code
        : EVALUATE_STATUS.NOT_RATE_YET.code;
    } else {
      newDataTable[index][field] = value;
    }

    setDataTable(newDataTable);
    const key = objectSearch?.level;
    setTableCache((prev: any) => ({
      ...prev,
      [key]: newDataTable,
    }));

    setTouched((prev) => ({
      ...prev,
      [`${field}_${index}`]: true,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const flattenData = Object.values(tableCache)?.flatMap((item: any) => item);
    const dataRequest = dataTable?.map((item: any) => {
      return {
        id: item?.id,
        criteriaSubsectionId: item?.criteriaSubsectionId,
        planCriteriaId: objectSearch?.planCriteriaId,
        fileId: item?.evidence?.id,
        fileName: item?.evidence?.name,
        pointSelfAssessment: item?.pointSelfAssessment?.id,
        conclusionAssessment: item?.conclusionAssessment?.id,
        comment: item?.comment,
        status: item?.status,
      };
    });
    setIsLoading(true);
    try {
      const { data } = await saveCriteria(dataRequest);
      if (data?.code === CODE_SUCCESS) {
        toast.success("Lưu thành công");
        setReload(!reload);
      }
    } catch (error) {
      console.error("error", error);
      toast.error(ERROR_MESSAGE);
    } finally {
      setIsLoading(false);
    }
    // const allTouched: { [key: string]: boolean } = {};
    // dataTable.forEach((row: any, index: number) => {
    //   allTouched[`pointSelfAssessment__${index}`] = true;
    //   allTouched[`evidence_${index}`] = true;
    //   allTouched[`conclusionAssessment_${index}`] = true;
    // });
    // setTouched(allTouched);

    // if (!Object.keys(errors)?.length) {
    //   console.log("Form values:", dataTable);
    // }
  };

  const isFieldDisabled = () => {
    return type === TYPE_MODAL.VIEW || type === TYPE_MODAL.EVALUATE;
  };

  const criteriaColumns = [
    {
      name: "Tên tiểu mục",
      field: "subsectionName",
      headerStyle: {
        width: "600px",
      },
      cellStyle: {
        ...textSystem,
      },
      render: (row: any, index: number, stt: number) => (
        <div onDoubleClick={() => handleDoubleClick(row)}>
          <span className={`${row?.conclusionAssessment ? "text-navy" : ""}`}>
            {row?.subsectionName}
          </span>
        </div>
      ),
    },
    {
      name:
        type === TYPE_MODAL.EVALUATE ? "Nhân viên tự đánh giá" : "Tự đánh giá",
      field: "pointSelfAssessment",
      cellStyle: {
        width: "200px",
        minWidth: "200px",
        textAlign: "center",
        ...textSystem,
      },
      render: (row: any, index: number, stt: number) => (
        <div>
          <AutocompleteV2
            options={RESULT}
            value={row?.pointSelfAssessment || null}
            name="pointSelfAssessment"
            onChange={(selectedOption) => {
              handleChangeTable(
                index,
                selectedOption,
                "pointSelfAssessment",
                row
              );
            }}
            className="spaces width-100"
            disabled={isFieldDisabled()}
          />
          {touched[`pointSelfAssessment_${index}`] &&
            errors[`pointSelfAssessment_${index}`] && (
              <div className="text-danger small mt-1">
                {errors[`pointSelfAssessment_${index}`]}
              </div>
            )}
        </div>
      ),
    },
    {
      name: "Bằng chứng",
      field: "evidence",
      cellStyle: {
        width: "200px",
        minWidth: "200px",
        textAlign: "center",
        ...textSystem,
      },
      render: (row: any, index: number, stt: number) => (
        <div className="d-flex align-items-center justify-content-center">
          <label className="custom-file-upload">
            <input
              type="file"
              onChange={(e) => {
                const file = e.target.files?.[0];
                handleChangeTable(index, file, "evidence", row);
              }}
              disabled={isFieldDisabled()}
            />
            {row?.evidence?.name ? (
              <span className="text-info">{row?.evidence?.name}</span>
            ) : (
              <span className="text-info">
                Tải tệp lên <i className="bi bi-upload text-navy ms-2"></i>
              </span>
            )}
          </label>
          {row?.evidence?.name && (
            <FileActions
              file={row?.evidence}
              onView={handleViewFile}
              onDownload={handleDownloadFile}
              disabled={isFieldDisabled()}
            />
          )}
          {touched[`evidence_${index}`] && errors[`evidence_${index}`] && (
            <div className="text-danger small mt-1">
              {errors[`evidence_${index}`]}
            </div>
          )}
        </div>
      ),
    },
    {
      name: "Hoàn thành đánh giá",
      field: "status",
      headerStyle: {
        width: "160px",
        minWidth: "160px",
      },
      cellStyle: {
        textAlign: "center",
        ...textSystem,
      },
      render: (row: any, index: number, stt: number) => (
        <div>
          <Form.Check
            type="checkbox"
            checked={row?.status === EVALUATE_STATUS.EVALUATED.code}
            name="status"
            onChange={(e) => {
              handleChangeTable(index, e.target.checked, "status", row);
            }}
            disabled={
              isFieldDisabled() ||
              row?.status === EVALUATE_STATUS.EVALUATED.code
            }
          />
        </div>
      ),
    },
    ...(type === TYPE_MODAL.EVALUATE
      ? [
          {
            name: "Kết luận",
            field: "conclusionAssessment",
            cellStyle: {
              width: "200px",
              minWidth: "200px",
              textAlign: "center",
              ...textSystem,
            },
            render: (row: any, index: number, stt: number) => (
              <div>
                <AutocompleteV2
                  options={RESULT}
                  value={row?.conclusionAssessment || null}
                  name="conclusionAssessment"
                  onChange={(selectedOption) => {
                    handleChangeTable(
                      index,
                      selectedOption,
                      "conclusionAssessment",
                      row
                    );
                  }}
                  className="spaces width-100"
                />
                {touched[`conclusionAssessment_${index}`] &&
                  errors[`conclusionAssessment_${index}`] && (
                    <div className="text-danger small mt-1">
                      {errors[`conclusionAssessment_${index}`]}
                    </div>
                  )}
              </div>
            ),
          },
          {
            name: "Hoàn thành đánh giá",
            field: "status",
            headerStyle: {
              width: "160px",
              minWidth: "160px",
            },
            cellStyle: {
              textAlign: "center",
              ...textSystem,
            },
            render: (row: any, index: number, stt: number) => (
              <div>
                <Form.Check
                  type="checkbox"
                  checked={row?.status === EVALUATE_STATUS.EVALUATED.code}
                  name="status"
                  onChange={(e) => {
                    handleChangeTable(index, e.target.checked, "status", row);
                  }}
                  disabled={row?.status === EVALUATE_STATUS.EVALUATED.code}
                />
              </div>
            ),
          },
          {
            name: "Ghi chú",
            field: "comment",
            cellStyle: {
              minWidth: "110px",
              ...textSystem,
            },
            render: (row: any, index: number, stt: number) => (
              <div>
                <TextValidator
                  className="spaces width-100"
                  name="comment"
                  value={row?.comment || ""}
                  onChange={(e: any) => {
                    handleChangeTable(index, e?.target?.value, "comment", row);
                  }}
                />
              </div>
            ),
          },
        ]
      : []),
  ];

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <OctTable
          id="searchCriteria"
          className={`table-background-th spaces h-calc-vh-242`}
          data={dataTable}
          columns={criteriaColumns}
          searchObject={objectSearch}
          setSearchObject={setObjectSearch}
          fixedColumnsCount={-1}
          noToolbar={true}
          totalPages={configTable.totalPages}
          totalElements={configTable.totalElements}
          numberOfElements={configTable.numberOfElements}
          isShowSelection={false}
          clearToolbar={true}
          noPagination={true}
        />

        {type !== TYPE_MODAL.VIEW && (
          <div className="position-fixed bottom-0 end-0 p-3 w-100 d-flex justify-content-end border-top">
            <Button className="btn-fill min-w-100px" type="submit">
              Lưu
            </Button>
          </div>
        )}
      </form>

      {viewFile?.open && (
        <ViewFile
          show={viewFile?.open}
          file={viewFile?.file}
          onHide={() => setViewFile({ open: false, file: null })}
        />
      )}
    </div>
  );
};

export default CriteriaTable;

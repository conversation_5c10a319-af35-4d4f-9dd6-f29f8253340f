import { IObject } from './GenerateFormConfig';

export interface IDialogProps {
	listFieldAuto?: any;
	onCloseClick?: () => void;
	handleSave?: (data: any) => void;
	onCancelClick?: () => void;
	title?: string;
	message?: string;
	isView?: boolean;
	isUpdate?: boolean;
	itemEdit?: IObject;
	validation?: IObject;
	isSave?: boolean;
	modelID?: any;
	handleSubmit?: any;
	setValues?: any;
	setTouched?: any;
	onChange?: any;
	values?: any;
	errors?: any;
	touched?: any;
	isValid?: any;
	handleChange?: any;
	customComponent?: any;
	setFieldValue?: any;
	customSearchObject?: any;
	readOnly?: boolean;
	fieldConditionalConfig?: Record<string, any>;
}

export interface ItemTypeProps {
	autofocus?: boolean;
	clearOnHide?: boolean;
	conditional?: IObject;
	defaultValue?: string;
	hidden?: boolean;
	input?: boolean;
	inputType?: string;
	inputFormat?: string;
	inputMask?: string;
	key: string;
	label?: string;
	lablelPosition?: string;
	multiple?: boolean;
	placeholder?: string;
	persistent?: boolean;
	prefix?: string;
	protected?: boolean;
	properties?: IObject;
	spellcheck?: boolean;
	suffix?: string;
	unique?: boolean;
	width?: number;
	offset?: number;
	push?: number;
	pull?: number;
	type?: string;
	value?: string;
	tag?: keyof JSX.IntrinsicElements;
	tags?: string[];
	validate?: IObject;
	columns?: IObject;
	values?: IObject;
	components?: ItemTypeProps[];
	data?: IObject;
	filePattern?: string;
	fileMaxSize?: string;
	fileMinSize?: string;
	format?: string;
	url?: string;
	fields?: any;
	content?: string;
	hideLabel?: boolean;
	inline?: boolean;
	className?: string;
	overlay?: any;
	searchObject?: any;
	length?: number;
	customClass?: string;
	cells?: any;
	searchData?: any;
	rows?: number;
	disabled?: boolean;
	customConditional?: string;
	[x: string]: any;
}

export type RequestQueue = {
	[key: string]: {
		promise: Promise<any>;
		resolve: (value: any) => void;
		reject: (reason?: any) => void;
	};
};

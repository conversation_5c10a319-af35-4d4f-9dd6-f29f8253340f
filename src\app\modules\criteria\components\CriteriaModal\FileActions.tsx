import React from "react";
import { IFile } from "../../../models/models";

interface FileActionButtonProps {
  onClick: () => void;
  title: string;
  iconClass: string;
  disabled?: boolean;
}

/**
 * Reusable file action button component
 */
export const FileActionButton: React.FC<FileActionButtonProps> = ({
  onClick,
  title,
  iconClass,
  disabled = false,
}) => (
  <div
    className={`spaces mt-2 ${disabled ? "text-muted" : ""}`}
    onClick={disabled ? undefined : onClick}
    title={title}
    style={{ 
      cursor: disabled ? "not-allowed" : "pointer",
      opacity: disabled ? 0.5 : 1 
    }}
  >
    <i className={`${iconClass} ms-2`}></i>
  </div>
);

interface FileActionsProps {
  file: IFile;
  onView: (file: IFile) => void;
  onDownload: (file: IFile) => void;
  disabled?: boolean;
}

/**
 * File actions component containing view and download buttons
 */
export const FileActions: React.FC<FileActionsProps> = ({
  file,
  onView,
  onDownload,
  disabled = false,
}) => (
  <>
    <FileActionButton
      onClick={() => onView(file)}
      title="Xem tệp"
      iconClass="bi bi-eye-fill"
      disabled={disabled}
    />
    <FileActionButton
      onClick={() => onDownload(file)}
      title="Tải xuống"
      iconClass="bi bi-download"
      disabled={disabled}
    />
  </>
);

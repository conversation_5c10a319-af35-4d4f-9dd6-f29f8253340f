import { toast } from "react-toastify";
import { actualFileType, DEFAULT_FILE_TYPES, ERROR_MESSAGE } from "./Constant";
import { fileUpload, getFileById } from "../services/fileServices";
import { IFile, IViewFile } from "../models/models";
import { saveAs } from 'file-saver';

// Constants
const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const DEFAULT_MIME_TYPE = "application/octet-stream";

const MIME_TYPE_MAP: Record<string, string> = {
  pdf: "application/pdf",
  docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  doc: "application/msword",
  xls: "application/vnd.ms-excel",
  jpg: "image/jpeg",
  jpeg: "image/jpeg",
  png: "image/png",
  bmp: "image/bmp",
  svg: "image/svg+xml",
  txt: "text/plain",
  csv: "text/csv",
};

// Utility functions
/**
 * Extracts file extension from filename
 */
const getFileExtension = (filename: string): string => {
  return filename.split(".").pop()?.toLowerCase() ?? "";
};

/**
 * Gets MIME type based on file extension
 */
const getMimeType = (extension: string): string => {
  return MIME_TYPE_MAP[extension] ?? DEFAULT_MIME_TYPE;
};

/**
 * Validates file type against allowed types
 */
const isValidFileType = (file: File): boolean => {
  return actualFileType.some(
    (item) => DEFAULT_FILE_TYPES.includes(item.id) && item.value === file.type
  );
};

/**
 * Validates file size against maximum allowed size
 */
const isValidFileSize = (file: File): boolean => {
  return file.size <= MAX_FILE_SIZE;
};

/**
 * Handles file upload with validation
 * @param file - File to upload
 * @param setIsLoading - Loading state setter function
 * @returns Promise<IFile | null> - Uploaded file info or null if failed
 */
export const handleUploadFileUtil = async (
  file: File,
  setIsLoading: (loading: boolean) => void
): Promise<IFile | null> => {
  // Validate file type
  if (!isValidFileType(file)) {
    toast.error("Sai kiểu tệp tin, vui lòng thử lại!");
    return null;
  }

  // Validate file size
  if (!isValidFileSize(file)) {
    toast.error("Tệp tin quá lớn, vui lòng thử lại!");
    return null;
  }

  setIsLoading(true);
  try {
    const {
      data: { data },
    } = await fileUpload(file);

    return {
      id: data?.id,
      name: data?.name,
    };
  } catch (error) {
    console.error("Upload file error", error);
    toast.error(ERROR_MESSAGE);
    return null;
  } finally {
    setIsLoading(false);
  }
};

/**
 * Handles file viewing by creating a blob URL
 * @param file - File to view
 * @param setIsLoading - Loading state setter function
 * @returns Promise<IViewFile | null> - View file object or null if failed
 */
export const handleViewFileUtil = async (
  file: IFile,
  setIsLoading: (loading: boolean) => void
): Promise<IViewFile | null> => {
  setIsLoading(true);
  try {
    const { data } = await getFileById(file?.id);
    const extension = getFileExtension(file?.name);
    const mimeType = getMimeType(extension);

    const blob = new Blob([data], { type: mimeType });
    const fileURL = URL.createObjectURL(blob);

    return {
      open: true,
      file: {
        id: file?.id,
        name: file?.name,
        type: extension,
        url: fileURL,
      },
    };
  } catch (error) {
    console.error("View file error", error);
    toast.error(ERROR_MESSAGE);
    return null;
  } finally {
    setIsLoading(false);
  }
};

/**
 * Handles file download using file-saver
 * @param file - File to download
 * @param setIsLoading - Loading state setter function
 * @returns Promise<boolean> - Success status
 */
export const handleDownloadFileUtil = async (
  file: IFile,
  setIsLoading: (loading: boolean) => void
): Promise<boolean> => {
  setIsLoading(true);
  try {
    const { data } = await getFileById(file?.id);
    const extension = getFileExtension(file?.name);
    const mimeType = getMimeType(extension);

    const blob = new Blob([data], { type: mimeType });
    saveAs(blob, file?.name);

    toast.success("Tải xuống thành công");
    return true;
  } catch (error) {
    console.error("Download file error", error);
    toast.error(ERROR_MESSAGE);
    return false;
  } finally {
    setIsLoading(false);
  }
};
